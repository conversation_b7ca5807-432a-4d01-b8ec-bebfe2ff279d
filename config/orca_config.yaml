# Orca Integration Configuration
# Direct Whirlpool program integration for reliable trading

# Orca Program Configuration
program:
  # Orca Whirlpool program address (mainnet/devnet)
  whirlpool_program_id: "whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc"
  
  # Token program addresses
  token_program_id: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
  associated_token_program_id: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
  
  # System program
  system_program_id: "11111111111111111111111111111111"

# Pool Configuration
pools:
  # SOL/USDC pool (primary trading pair)
  SOL_USDC:
    pool_address: "HJPjoWUrhoZzkNfRpHuieeFk9WcZWjwy6PBjZ81ngndJ"  # Example - will be discovered
    token_a: "So11111111111111111111111111111111111111112"  # SOL
    token_b: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"  # USDC
    fee_tier: 0.0025  # 0.25% fee
    tick_spacing: 64
    
  # Additional pools can be added here
  # SOL_USDT:
  #   pool_address: "..."
  #   token_a: "So11111111111111111111111111111111111111112"  # SOL
  #   token_b: "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"  # USDT
  #   fee_tier: 0.0025
  #   tick_spacing: 64

# Token Configuration
tokens:
  SOL:
    mint: "So11111111111111111111111111111111111111112"
    decimals: 9
    symbol: "SOL"
    name: "Solana"
    
  USDC:
    mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    decimals: 6
    symbol: "USDC"
    name: "USD Coin"
    
  USDT:
    mint: "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"
    decimals: 6
    symbol: "USDT"
    name: "Tether USD"

# Transaction Configuration
transaction:
  # Slippage tolerance (basis points)
  default_slippage_bps: 50  # 0.5%
  max_slippage_bps: 200     # 2.0%
  
  # Price impact limits
  max_price_impact_pct: 2.0
  warning_price_impact_pct: 1.0
  
  # Compute unit limits
  compute_unit_limit: 200000
  compute_unit_price_micro_lamports: 1000
  
  # Transaction options
  skip_preflight: true
  max_retries: 3
  commitment: "confirmed"

# Pool Discovery
discovery:
  # Whether to auto-discover pools
  auto_discover: true
  
  # Pool discovery sources
  sources:
    - "rpc"      # Discover via RPC calls
    - "static"   # Use static configuration
  
  # Cache settings
  cache_duration_minutes: 60
  refresh_on_error: true

# Risk Management
risk:
  # Minimum liquidity requirements
  min_pool_liquidity_usd: 10000
  min_token_balance_sol: 0.01
  
  # Maximum trade sizes
  max_trade_size_pct: 10.0  # 10% of pool liquidity
  max_position_size_sol: 1.0
  
  # Circuit breaker
  max_consecutive_failures: 5
  failure_cooldown_minutes: 15

# Monitoring
monitoring:
  # Pool health checks
  check_pool_health: true
  health_check_interval_seconds: 300
  
  # Price monitoring
  track_price_changes: true
  price_change_threshold_pct: 5.0
  
  # Liquidity monitoring
  track_liquidity_changes: true
  liquidity_change_threshold_pct: 20.0

# Performance Optimization
optimization:
  # Instruction optimization
  use_compute_budget: true
  optimize_instruction_order: true
  
  # Account optimization
  use_lookup_tables: false  # Not needed for simple swaps
  batch_instructions: false # Keep simple for reliability
  
  # Timing optimization
  immediate_execution: true
  max_build_time_seconds: 5
  max_sign_time_seconds: 2

# Fallback Configuration
fallback:
  # Enable fallback to simple transfers if swap fails
  enable_fallback: false
  
  # Fallback options
  fallback_to_jupiter: false  # Jupiter deprecated
  fallback_to_raydium: false  # Not implemented
  
  # Error handling
  retry_on_simulation_failure: true
  retry_on_blockhash_error: true
  max_fallback_attempts: 2

# Development/Testing
development:
  # Test mode settings
  use_devnet: false
  simulate_only: false
  
  # Logging
  verbose_logging: true
  log_instructions: true
  log_accounts: false
  
  # Testing
  enable_test_mode: false
  test_pool_override: null

# Environment Overrides
environments:
  development:
    transaction:
      default_slippage_bps: 100  # Higher slippage for testing
      skip_preflight: true
    development:
      verbose_logging: true
      use_devnet: true
      
  production:
    transaction:
      default_slippage_bps: 50   # Conservative slippage
      max_retries: 5
    risk:
      max_price_impact_pct: 1.0  # Stricter limits
    development:
      verbose_logging: false
      simulate_only: false
