#!/usr/bin/env python3
"""
Current System Integration Tests

Tests aligned with our current working live trading system.
"""

import pytest
import asyncio
import os
import sys
from unittest.mock import Mock, patch
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

class TestCurrentSystemIntegration:
    """Test suite for current working system."""

    @pytest.mark.asyncio
    async def test_unified_live_trader_initialization(self):
        """Test UnifiedLiveTrader initialization."""
        from scripts.unified_live_trading import UnifiedLiveTrader

        trader = UnifiedLiveTrader()

        # Test environment validation
        assert hasattr(trader, 'validation_errors')
        assert isinstance(trader.validation_errors, list)

        # Test configuration loading
        assert hasattr(trader, 'wallet_address')
        assert hasattr(trader, 'helius_api_key')
        assert hasattr(trader, 'dry_run')

    @pytest.mark.asyncio
    async def test_component_initialization(self):
        """Test component initialization without errors."""
        from scripts.unified_live_trading import UnifiedLiveTrader

        trader = UnifiedLiveTrader()

        # Mock environment variables to avoid validation errors
        with patch.dict(os.environ, {
            'WALLET_ADDRESS': 'test_address',
            'HELIUS_API_KEY': 'test_key',
            'KEYPAIR_PATH': 'test_path'
        }):
            # Should not raise exceptions during initialization
            trader_with_env = UnifiedLiveTrader()
            assert len(trader_with_env.validation_errors) == 1  # keypair file not found

    def test_telegram_notifier_initialization(self):
        """Test Telegram notifier initialization."""
        from core.notifications.telegram_notifier import TelegramNotifier

        notifier = TelegramNotifier()

        # Should initialize without errors
        assert hasattr(notifier, 'enabled')
        assert hasattr(notifier, 'bot_token')
        assert hasattr(notifier, 'chat_id')

    def test_helius_client_initialization(self):
        """Test Helius client initialization."""
        from phase_4_deployment.rpc_execution.helius_client import HeliusClient

        client = HeliusClient(api_key="test_key")

        # Should initialize without errors
        assert hasattr(client, 'api_key')
        assert hasattr(client, 'rpc_url')

    def test_tx_builder_initialization(self):
        """Test transaction builder initialization."""
        from phase_4_deployment.rpc_execution.tx_builder import TxBuilder

        # Use a valid Solana address for testing
        valid_address = "J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz"
        builder = TxBuilder(valid_address)

        # Should initialize without errors
        assert hasattr(builder, 'wallet_address')
        assert hasattr(builder, 'jupiter_config')

    def test_transaction_executor_initialization(self):
        """Test transaction executor initialization."""
        from phase_4_deployment.rpc_execution.transaction_executor import TransactionExecutor
        from phase_4_deployment.rpc_execution.helius_client import HeliusClient

        client = HeliusClient(api_key="test_key")
        executor = TransactionExecutor(rpc_client=client)

        # Should initialize without errors
        assert hasattr(executor, 'rpc_client')
        assert hasattr(executor, 'max_retries')

    @pytest.mark.asyncio
    async def test_fallback_trading_cycle(self):
        """Test fallback trading cycle."""
        from scripts.unified_live_trading import UnifiedLiveTrader

        trader = UnifiedLiveTrader()

        # Mock components to avoid actual API calls
        trader.trading_enabled = False
        trader.dry_run = True

        result = await trader.run_fallback_trading_cycle()

        # Should return valid result structure
        assert isinstance(result, dict)
        assert 'signals_generated' in result
        assert 'signals_enriched' in result
        assert 'trade_executed' in result
        assert 'mode' in result
        assert result['mode'].startswith('fallback')

    def test_configuration_files_exist(self):
        """Test that required configuration files exist."""
        required_configs = [
            "config.yaml",
            "config/token_registry.yaml",
            ".env"
        ]

        for config in required_configs:
            assert os.path.exists(config), f"Required config file missing: {config}"

    def test_essential_scripts_exist(self):
        """Test that essential scripts exist."""
        essential_scripts = [
            "scripts/unified_live_trading.py",
            "scripts/test_fixed_live_trading.py",
            "scripts/rich_trade_analyzer.py",
            "phase_4_deployment/unified_runner.py"
        ]

        for script in essential_scripts:
            assert os.path.exists(script), f"Essential script missing: {script}"

    def test_essential_components_exist(self):
        """Test that essential components exist."""
        essential_components = [
            "phase_4_deployment/rpc_execution/",
            "core/notifications/",
            "output/",
            "logs/"
        ]

        for component in essential_components:
            assert os.path.exists(component), f"Essential component missing: {component}"

    @pytest.mark.asyncio
    async def test_wallet_balance_check_structure(self):
        """Test wallet balance check structure (without actual API call)."""
        from scripts.unified_live_trading import UnifiedLiveTrader

        trader = UnifiedLiveTrader()

        # Mock the API call to avoid actual network request
        with patch.object(trader, 'check_wallet_balance') as mock_check:
            mock_check.return_value = True

            result = await trader.check_wallet_balance()
            assert result is True

    def test_trade_record_structure(self):
        """Test trade record structure."""
        # Check if trade records directory exists
        trades_dir = "output/live_production/trades"

        if os.path.exists(trades_dir):
            trade_files = [f for f in os.listdir(trades_dir) if f.endswith('.json')]

            if trade_files:
                import json

                # Check structure of most recent trade record
                latest_trade = max(trade_files)
                with open(os.path.join(trades_dir, latest_trade), 'r') as f:
                    trade_data = json.load(f)

                # Verify required fields
                required_fields = ['timestamp', 'signal', 'result', 'execution_time', 'wallet_address', 'mode']
                for field in required_fields:
                    assert field in trade_data, f"Required field missing in trade record: {field}"

    def test_session_proof_structure(self):
        """Test session proof structure."""
        # Check if session proofs directory exists
        proofs_dir = "output/session_proofs"

        if os.path.exists(proofs_dir):
            proof_files = [f for f in os.listdir(proofs_dir) if f.endswith('.json')]

            if proof_files:
                import json

                # Check structure of most recent proof
                latest_proof = max(proof_files)
                with open(os.path.join(proofs_dir, latest_proof), 'r') as f:
                    proof_data = json.load(f)

                # Verify required fields
                required_fields = [
                    'session_timestamp', 'session_duration_minutes', 'wallet_address',
                    'starting_balance_sol', 'ending_balance_sol', 'balance_change_sol',
                    'session_data', 'proof_verified'
                ]
                for field in required_fields:
                    assert field in proof_data, f"Required field missing in session proof: {field}"

class TestSystemHealth:
    """Test system health and readiness."""

    def test_no_import_conflicts(self):
        """Test that there are no import conflicts."""
        try:
            from scripts.unified_live_trading import UnifiedLiveTrader
            from core.notifications.telegram_notifier import TelegramNotifier
            from phase_4_deployment.rpc_execution.helius_client import HeliusClient
            from phase_4_deployment.rpc_execution.tx_builder import TxBuilder
            from phase_4_deployment.rpc_execution.transaction_executor import TransactionExecutor
        except ImportError as e:
            pytest.fail(f"Import conflict detected: {e}")

    def test_environment_variables_structure(self):
        """Test environment variables structure."""
        from dotenv import load_dotenv
        load_dotenv()

        # Check that critical environment variables are defined (even if empty)
        critical_vars = ['WALLET_ADDRESS', 'HELIUS_API_KEY', 'TELEGRAM_BOT_TOKEN', 'TELEGRAM_CHAT_ID']

        for var in critical_vars:
            # Variable should be defined in .env file (can be empty for testing)
            assert var in os.environ or os.getenv(var) is not None, f"Environment variable not defined: {var}"

    def test_directory_structure(self):
        """Test that required directory structure exists."""
        required_dirs = [
            "scripts/",
            "phase_4_deployment/",
            "core/",
            "config/",
            "output/",
            "logs/",
            "wallet/"
        ]

        for directory in required_dirs:
            assert os.path.exists(directory), f"Required directory missing: {directory}"

    def test_no_deprecated_imports(self):
        """Test that deprecated modules are not being imported by our current system."""
        # This test ensures our current system doesn't accidentally import deprecated modules
        # Note: Some deprecated modules may still exist but should not be used by current system

        # Test that our current system imports work correctly
        try:
            from scripts.unified_live_trading import UnifiedLiveTrader
            from core.notifications.telegram_notifier import TelegramNotifier
            from phase_4_deployment.rpc_execution.helius_client import HeliusClient

            # If we get here, our current system imports are working
            assert True, "Current system imports working correctly"

        except ImportError as e:
            pytest.fail(f"Current system import failed: {e}")

        # Test that we're not accidentally using old config patterns
        import sys
        current_modules = [name for name in sys.modules.keys() if 'phase_1_' in name or 'phase_2_' in name or 'phase_3_' in name]

        # Should not have old phase modules loaded by our current system
        problematic_modules = [mod for mod in current_modules if any(old in mod for old in ['strategy_runner', 'risk_management', 'rl_agent'])]

        if problematic_modules:
            print(f"Warning: Old phase modules detected: {problematic_modules}")
            # Don't fail the test, just warn - these might be in backup directories

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
