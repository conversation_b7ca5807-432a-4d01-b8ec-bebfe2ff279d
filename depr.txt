# Deprecated Files List
# This file tracks deprecated files that have been replaced by new implementations

# ===== DEPRECATED DIRECTORIES =====

# Old phase directories (replaced by phase_4_deployment)
phase_0_env_setup/
phase_1_strategy_runner/
phase_2_strategy/
phase_3_rl_agent_training/

# Redundant backtest directories (replaced by phase_4_deployment/backtest)
phase_2_backtest_engine/data/
phase_2_backtest_engine/output/
phase_2_backtest_engine/outputs/

# Backup directories
backup/
backups/
phase_4_deployment/backup_dashboards/

# Redundant output directories
output/
output/dashboard/
output/dashboard_tests/
output/live_trade_test/
output/live_trading_logs/
output/logs/
output/mean_reversion/
output/momentum/
output/paper_trading/
output/production_tests/
output/strategy_comparison/
output/validation/
output/visualizations/

# Redundant data directories
data/
data/prepared/
data/raw/
core/data/

# ===== DEPRECATED FILES =====

# Old data files
phase_0_env_setup/data/historical/sol_usdc.csv

# ===== NEW DEPRECATIONS FOR WHALE+RL IMPLEMENTATION =====

# Superseded by whale-RL integrated versions
scripts/enhanced_live_trading.py                  # Superseded by whale_rl_live_trading.py
scripts/rl_enhanced_live_trading.py              # Superseded by whale_rl_live_trading.py
core/learning/adaptive_strategy_manager.py        # Superseded by enhanced_adaptive_manager.py

# Basic analysis tools (replaced by whale-aware versions)
scripts/calculate_session_pnl.py                 # Superseded by whale_performance_analyzer.py

# Legacy trading systems (will be superseded)
scripts/enhanced_live_trading.py                 # Will be superseded by whale_rl_live_trading.py

# Basic signal generation (will be replaced by whale-fusion engine)
# Any basic signal generators will be superseded by signal_fusion_engine.py

# Simple dashboard components (will be enhanced with whale data)
# Basic dashboards will be enhanced with whale monitoring capabilities
phase_0_env_setup/data/historical/jup_usdc.csv
phase_0_env_setup/data/historical/bonk_usdc.csv

# Old strategy implementations
phase_1_strategy_runner/strategies/momentum_strategy.py
phase_1_strategy_runner/strategies/mean_reversion_strategy.py
phase_1_strategy_runner/strategies/breakout_strategy.py
phase_1_strategy_runner/strategies/basic_ma_strategy.py
phase_1_strategy_runner/strategies/meme_alpha_strategy.py
phase_2_strategy/momentum_strategy.py
phase_2_strategy/run_strategy_finder.py
phase_2_strategy/strategy_finder.py
phase_2_strategy/visualize_strategy.py
core/engine/strategy_runner.py

# Old signal generators
phase_1_strategy_runner/signal_generator.py
phase_1_strategy_runner/signal_processor.py
phase_1_strategy_runner/runners/strategy_runner.py

# Old backtest files
phase_2_backtest_engine/backtest_runner.py
phase_2_backtest_engine/backtest_visualizer.py
phase_2_backtest_engine/backtest_metrics.py
phase_2_backtest_engine/utils/strategy_loader.py

# Old simulation files
phase_4_deployment/simulation/simple_simulator.py
phase_4_deployment/simulation/market_simulator.py

# Redundant dashboard files
phase_4_deployment/backup_dashboards/dashboard_simulator.py
phase_4_deployment/backup_dashboards/run_streamlit_dashboard.py
phase_4_deployment/backup_dashboards/streamlit_dashboard.py
phase_4_deployment/backup_dashboards/test_streamlit_dashboard.py
phase_4_deployment/fix_dashboard.py
phase_4_deployment/remove_old_dashboards.py
phase_4_deployment/verify_metrics_dashboard.py
import_dashboard.py
run_dashboard.py

# Redundant test files
phase_4_deployment/test_birdeye.py
phase_4_deployment/test_helius.py
phase_4_deployment/test_monitoring_components.py
phase_4_deployment/test_monitoring.py
phase_4_deployment/test_paper_trading.py
phase_4_deployment/test_python_comm_layer.py
phase_4_deployment/test_stream_data_ingestor.py
phase_4_deployment/test_system.py
phase_4_deployment/test_transaction.py
test_birdeye_api.py
test_carbon_core.py
test_helius.py
test_solana_tx_utils.py
test_transaction.py

# Redundant configuration files
config_example.yaml
configs/config_example.yaml

# ===== JUPITER INTEGRATION CLEANUP (2025-05-24) =====
# HIGHEST PRIORITY: Files that conflict with our new production Jupiter system

# OLD TRANSACTION BUILDERS (REPLACED BY phase_4_deployment/rpc_execution/tx_builder.py)
core/transaction/enhanced_tx_builder.py
core/transaction/enhanced_tx_executor.py
core/wallet/secure_wallet_manager.py

# OLD TRANSACTION EXECUTORS (REPLACED BY phase_4_deployment/rpc_execution/transaction_executor.py)
core/execution/transaction_executor.py
phase_4_deployment/transaction_execution/transaction_executor.py

# OLD JUPITER TEST FILES (REPLACED BY scripts/test_jupiter_swap.py)
scripts/test_transaction_fixes.py
scripts/enhanced_live_trading.py

# OLD SOLANA TX UTILS (CAUSING IMPORT CONFLICTS)
solana_tx_utils_fallback/
shared/solana_utils/tx_utils/
shared/solana_utils/fallback/

# OLD WALLET MANAGERS (REPLACED BY DIRECT KEYPAIR LOADING)
core/wallet/
keys/test_wallet_keypair.json
keys/wallet_keypair.json
keys/paper_wallet.json

# DUPLICATE TRANSACTION PREPARATION SERVICES
phase_4_deployment/transaction_preparation/
rust_tx_prep_service/

# OLD LIVE TRADING SYSTEMS (REPLACED BY scripts/production_ready_trader.py)
enhanced_live_trading_system.py
start_live_trading_local.py
small_live_trade_test.py
run_devnet_paper_trade.py

# OLD DASHBOARD IMPLEMENTATIONS (REPLACED BY phase_4_deployment/unified_dashboard/)
enhanced_trading_dashboard.py
simple_monitoring_dashboard.py
enhanced_paper_trading_monitor.py
live_trade_test_dashboard.py

# OLD RUNNER IMPLEMENTATIONS (REPLACED BY scripts/production_ready_trader.py)
run_synergy7.py
run_q5_system.py
unified_runner.py
synergy7_runner.py

# DUPLICATE CONFIGURATION FILES (CAUSING CONFUSION)
config_example.yaml
configs/config_example.yaml
phase_4_deployment/config.yaml
phase_4_deployment/config.yaml.bak
phase_4_deployment/test_config.yaml
phase_4_deployment/test_config.yaml.bak
phase_4_deployment/test_config_clean.yaml

# OLD TEST FILES (REPLACED BY scripts/test_jupiter_swap.py AND scripts/test_transaction_signing.py)
test_transaction.py
test_solana_tx_utils.py
test_helius.py
test_carbon_core.py
direct_telegram_test.py
test_telegram.py
test_trade_notification.py
test_trading_alerts.py

# OLD PHASE DIRECTORIES (REPLACED BY phase_4_deployment/)
phase_0_env_setup/
phase_1_strategy_runner/
phase_2_backtest_engine/
phase_2_strategy/
phase_3_rl_agent_training/

# OLD UTILITY SCRIPTS (REPLACED BY PRODUCTION SYSTEM)
identify_duplicates.py
update_imports.py
generate_test_metrics.py
setup_production_env.py
validate_production_setup.py
run_tests.py

# OLD BACKUP DIRECTORIES (NO LONGER NEEDED)
backup/
backups/
phase_4_deployment/backup_dashboards/

# OLD OUTPUT DIRECTORIES (REPLACED BY phase_4_deployment/output/)
output/dashboard/
output/dashboard_tests/
output/live_trade_test/
output/live_trading_logs/
output/logs/
output/mean_reversion/
output/momentum/
output/paper_trading/
output/production_tests/
output/strategy_comparison/
output/validation/
output/visualizations/

# OLD DATA DIRECTORIES (REPLACED BY CENTRALIZED DATA/)
data/prepared/
data/raw/
core/data/

# ===== ADDITIONAL DEPRECATED FILES (Integration Plan) =====

# Old market regime detection (replaced by enhanced version)
phase_2_strategy/market_regime.py

# Redundant strategy implementations (replaced by unified core strategies)
phase_2_strategy/momentum_strategy.py
phase_2_strategy/mean_reversion.py
phase_2_strategy/risk_management.py
backup/mean_reversion/

# Old whale watching (replaced by enhanced signal generator)
phase_4_deployment/data_router/whale_watcher.py
phase_4_deployment/data_router/birdeye_scanner.py

# Redundant risk management (replaced by enhanced core risk)
phase_2_strategy/risk_management.py

# Old strategy runners (replaced by unified runner)
phase_1_strategy_runner/runners/strategy_runner.py
core/engine/strategy_runner.py

# Redundant configuration files
live_trade_test_config.yaml
paper_trade_config.yaml
carbon_core_config.yaml
carbon_core_fallback_config.yaml

# Redundant test files
test_telegram.py
test_trade_notification.py
test_trading_alerts.py
direct_telegram_test.py

# Redundant runner files
run_synergy7.py
run_q5_system.py
start_live_trading_local.py
unified_runner.py (root level)
paper_trading_simulator.py

# ===== REPLACEMENT INFORMATION =====

# These deprecated files and directories have been replaced by the new implementations in:
# - core/strategies/ - Enhanced strategy implementations with regime detection
# - core/data/ - Unified data ingestion and whale signal generation
# - core/risk/ - Advanced risk management with VaR/CVaR
# - core/analytics/ - Strategy performance attribution and analysis
# - core/signals/ - Unified signal processing and enrichment
# - phase_4_deployment/unified_runner.py - Single entry point for all modes
# - config.yaml - Centralized configuration with all parameters

# The new integrated system provides:
# 1. Enhanced market regime detection with probabilistic models
# 2. Whale watching integration as complementary alpha source
# 3. Advanced risk management with VaR/CVaR and correlation analysis
# 4. Strategy performance attribution and adaptive weighting
# 5. Configuration-driven architecture avoiding hard-coded values
# 6. Unified entry point with proper mode switching
# 7. Comprehensive monitoring and alerting system

# ===== ENHANCED INTEGRATION FILES (DO NOT DELETE) =====

# Phase 1: Enhanced Market Regime Detection & Whale Watching
# core/strategies/market_regime_detector.py - Enhanced regime detection with dynamic thresholds
# core/strategies/probabilistic_regime.py - ML-based regime detection with HMM
# core/data/whale_signal_generator.py - Whale transaction monitoring and signal generation
# core/signals/whale_signal_processor.py - Whale signal processing and validation

# Phase 2: Advanced Risk Management
# core/risk/var_calculator.py - VaR/CVaR calculation with multiple methodologies
# core/risk/portfolio_risk_manager.py - Portfolio-level risk monitoring and correlation analysis
# core/risk/position_sizer.py (enhanced) - VaR-based and regime-aware position sizing

# Phase 3: Strategy Performance Attribution
# core/analytics/strategy_attribution.py - Individual strategy performance tracking
# core/analytics/performance_analyzer.py - Portfolio analysis and optimization recommendations

# Phase 4: Adaptive Strategy Weighting
# core/strategies/adaptive_weight_manager.py - Dynamic weight adjustment based on performance
# core/strategies/strategy_selector.py - Intelligent strategy selection and management

# Integration Test Files
# test_phase1_integration.py - Phase 1 validation tests
# test_phase2_simple.py - Phase 2 validation tests
# test_phase3_attribution.py - Phase 3 validation tests
# test_phase4_adaptive_weighting.py - Phase 4 validation tests
# test_complete_integration.py - End-to-end integration validation

# Documentation Files
# PHASE1_IMPLEMENTATION_SUMMARY.md - Phase 1 detailed documentation
# PHASE2_IMPLEMENTATION_SUMMARY.md - Phase 2 detailed documentation
# PHASE4_IMPLEMENTATION_SUMMARY.md - Phase 4 detailed documentation
# COMPLETE_INTEGRATION_SUMMARY.md - Comprehensive integration summary
# DEPLOYMENT_CHECKLIST.md - Production deployment checklist

# Enhanced Configuration
# config.yaml (enhanced) - Comprehensive configuration with all new parameters

# ===== REDUNDANT SCRIPTS (SUPERSEDED BY ENHANCED TRADING SYSTEM) =====

# Old paper trading implementations (replaced by simple_paper_trading_monitor.py)
start_paper_trading.py
run_devnet_paper_trade.py
paper_trading_simulator.py

# Old dashboard implementations (replaced by enhanced_trading_dashboard.py and simple_monitoring_dashboard.py)
import_dashboard.py
run_dashboard.py
phase_4_deployment/run_monitoring.py
phase_4_deployment/unified_dashboard/run_dashboard.py
phase_4_deployment/monitoring/streamlit_dashboard.py
phase_4_deployment/fix_dashboard.py
phase_4_deployment/verify_metrics_dashboard.py
phase_4_deployment/remove_old_dashboards.py

# Old monitoring implementations (replaced by enhanced monitoring suite)
core/monitoring/performance_monitor.py
utils/monitoring/monitoring_service.py
shared/utils/monitoring.py
scripts/system_health_monitor.py
scripts/test_monitoring_setup.py
phase_4_deployment/monitoring/monitoring_service.py
phase_4_deployment/monitoring/setup_monitoring.py
phase_4_deployment/monitoring/mock_monitoring_service.py
phase_4_deployment/monitoring/pyo3_extension_monitor.py
phase_4_deployment/utils/monitoring.py
phase_4_deployment/core/jito_monitor.py

# Old test files (replaced by enhanced validation system)
test_telegram.py
test_transaction.py
test_helius.py
direct_telegram_test.py
test_phase2_risk_management.py
test_phase4_adaptive_weighting.py
phase_4_deployment/test_monitoring_components.py
phase_4_deployment/test_monitoring.py
phase_4_deployment/test_paper_trading.py
phase_4_deployment/test_birdeye.py
phase_4_deployment/test_helius.py
phase_4_deployment/test_system.py
phase_4_deployment/test_transaction.py
phase_4_deployment/tests/test_unified_dashboard.py
phase_4_deployment/tests/test_streamlit_dashboard.py
phase_4_deployment/scripts/test_dashboard.py

# Old runner implementations (replaced by enhanced paper trading monitor)
run_synergy7.py
run_q5_system.py
start_live_trading_local.py
unified_runner.py (root level)

# Old setup and utility scripts (replaced by enhanced system)
setup_production_env.py
validate_production_setup.py
generate_test_metrics.py
identify_duplicates.py
update_imports.py
run_tests.py

# Failed enhanced implementation (replaced by simple_paper_trading_monitor.py)
enhanced_paper_trading_monitor.py

# Backup dashboard files (all superseded)
phase_4_deployment/backup_dashboards/dashboard_simulator.py
phase_4_deployment/backup_dashboards/run_streamlit_dashboard.py
phase_4_deployment/backup_dashboards/streamlit_dashboard.py
phase_4_deployment/backup_dashboards/test_streamlit_dashboard.py
phase_4_deployment/backup_dashboards/test_monitoring_components.py

# ===== REPLACEMENT SUMMARY FOR ENHANCED TRADING SYSTEM =====

# The following files have been REPLACED by the Enhanced Trading System:

# OLD PAPER TRADING → NEW ENHANCED PAPER TRADING
# start_paper_trading.py → simple_paper_trading_monitor.py
# run_devnet_paper_trade.py → simple_paper_trading_monitor.py
# paper_trading_simulator.py → simple_paper_trading_monitor.py

# OLD DASHBOARDS → NEW DASHBOARD SUITE
# import_dashboard.py → enhanced_trading_dashboard.py
# run_dashboard.py → enhanced_trading_dashboard.py
# phase_4_deployment/unified_dashboard/run_dashboard.py → enhanced_trading_dashboard.py
# phase_4_deployment/monitoring/streamlit_dashboard.py → simple_monitoring_dashboard.py

# OLD MONITORING → NEW MONITORING SUITE
# core/monitoring/performance_monitor.py → core/monitoring/system_metrics.py
# scripts/system_health_monitor.py → simple_monitoring_dashboard.py
# phase_4_deployment/monitoring/monitoring_service.py → simple_monitoring_dashboard.py

# OLD VALIDATION → NEW ENHANCED VALIDATION
# test_phase2_risk_management.py → Integrated in simple_paper_trading_monitor.py
# test_phase4_adaptive_weighting.py → Integrated in simple_paper_trading_monitor.py
# validate_production_setup.py → Integrated validation in enhanced system

# The Enhanced Trading System provides:
# 1. ✅ Real-time 4-phase trading system simulation
# 2. ✅ Comprehensive dashboard suite with live monitoring
# 3. ✅ Integrated validation through paper trading cycles
# 4. ✅ System health monitoring with API status tracking
# 5. ✅ Performance attribution and adaptive strategy weighting
# 6. ✅ VaR/CVaR risk management with real-time calculations
# 7. ✅ Market regime detection with confidence levels
# 8. ✅ Whale signal monitoring and processing
# 9. ✅ Auto-refresh dashboards with configurable intervals
# 10. ✅ Comprehensive data generation for analysis

# ===== TRANSACTION FIXES - DEPRECATED FILES (2025-05-24) =====

# Old transaction implementations (replaced by enhanced versions)
phase_4_deployment/rpc_execution/transaction_executor.py → core/transaction/enhanced_tx_executor.py
phase_4_deployment/rpc_execution/tx_builder.py → core/transaction/enhanced_tx_builder.py
phase_4_deployment/start_live_trading.py → scripts/enhanced_live_trading.py
wallet/trading_wallet_keypair.json (old invalid keypair) → wallet/trading_wallet_keypair.json (new valid)

# Redundant environment files (can be archived)
.env.active_simulation.bak
.env.bak.bak
.env.backup.bak
.env.simulation.bak
phase_4_deployment/sample.env

# ===== ACTIVE ENHANCED SYSTEM FILES (DO NOT DEPRECATE) =====

# Enhanced Transaction System (NEW - 2025-05-24):
# core/transaction/enhanced_tx_builder.py - Complete transaction building with Jupiter integration
# core/transaction/enhanced_tx_executor.py - Multi-RPC execution with retry logic
# core/wallet/secure_wallet_manager.py - Secure keypair management and validation
# scripts/enhanced_live_trading.py - Main enhanced live trading system
# scripts/test_transaction_fixes.py - Comprehensive transaction validation
# scripts/generate_test_keypair.py - Secure keypair generation

# Enhanced Trading System Core Files:
# simple_paper_trading_monitor.py - Main enhanced paper trading monitor
# enhanced_trading_dashboard.py - Real-time trading strategy dashboard
# simple_monitoring_dashboard.py - System health and API monitoring dashboard

# Enhanced Core Components:
# core/strategies/market_regime_detector.py - Enhanced market regime detection
# core/strategies/probabilistic_regime.py - Probabilistic regime detection
# core/strategies/adaptive_weight_manager.py - Adaptive strategy weighting
# core/strategies/strategy_selector.py - Intelligent strategy selection
# core/risk/var_calculator.py - VaR/CVaR risk calculations
# core/risk/portfolio_risk_manager.py - Portfolio risk management
# core/data/whale_signal_generator.py - Whale transaction monitoring
# core/signals/whale_signal_processor.py - Whale signal processing
# core/analytics/strategy_attribution.py - Strategy performance attribution
# core/analytics/performance_analyzer.py - Performance analysis

# DEPRECATED TESTS (May 24, 2025 - System Update):
# tests/test_momentum_optimizer.py - Replaced by enhanced strategy components
# tests/test_portfolio_limits.py - Replaced by portfolio_risk_manager tests
# tests/test_position_sizer.py - Replaced by production_position_sizer tests
# tests/test_stop_loss.py - Integrated into risk management tests
# tests/test_circuit_breaker.py - Integrated into system monitoring tests
# phase_4_deployment/tests/test_liljito_client.py - Lil Jito integration deprecated
# phase_4_deployment/tests/test_stream_data_ingestor.py - Stream data approach changed
# phase_4_deployment/tests/test_streamlit_dashboard.py - Dashboard architecture updated
# phase_4_deployment/tests/test_live_trading.py - Live trading architecture completely redesigned
# scripts/test_monitoring_setup.py - Monitoring system redesigned
# scripts/test_transaction_fixes.py - Transaction system redesigned
# scripts/integration_test.py - Integration approach changed
# scripts/test_dashboard_metrics.py - Dashboard metrics approach changed
# core/monitoring/system_metrics.py - System health monitoring

# Enhanced Documentation:

# ===== REDUNDANT ENTRY POINTS (2025-05-25 - UNIFIED RUNNER CONSOLIDATION) =====

# REDUNDANT LIVE TRADING ENTRY POINTS - KEEP ONLY 2 ENTRY POINTS
# PRIMARY: scripts/unified_live_trading.py (direct execution, Jito Bundle support)
# SECONDARY: phase_4_deployment/start_live_trading.py (advanced features, core implementation)

# REDUNDANT WRAPPER SCRIPTS (all call the 2 main entry points)
scripts/production_ready_trader.py              # Redundant wrapper - calls phase_4_deployment/start_live_trading.py
scripts/start_live_production.py                # Redundant wrapper - calls phase_4_deployment/start_live_trading.py
scripts/opportunistic_live_trading.py           # Redundant wrapper - calls scripts/unified_live_trading.py
scripts/execute_10_minute_session.py            # Redundant wrapper - calls scripts/unified_live_trading.py
scripts/quick_production_test.py                # Redundant test script - not production entry point
scripts/whale_rl_live_trading.py                # Redundant specialized script
start_paper_trading.py                          # Redundant - use phase_4_deployment/unified_runner.py --mode paper
unified_runner.py (root level)                  # Redundant - use phase_4_deployment/unified_runner.py
core/engine/unified_runner.py                   # Redundant - use phase_4_deployment/unified_runner.py

# Redundant dashboard entry points (integrated into unified_runner.py)
run_dashboard.py                                 # Redundant - dashboard starts with unified_runner.py
enhanced_trading_dashboard.py                   # Redundant - use unified_runner.py with dashboard
simple_monitoring_dashboard.py                  # Redundant - use unified_runner.py with dashboard

# Redundant test entry points (use unified testing approach)
scripts/test_jupiter_swap.py                    # Redundant - use unified testing
scripts/test_transaction_signing.py             # Redundant - use unified testing

# REPLACEMENT INFORMATION:
# ALL TRADING OPERATIONS SHOULD USE: phase_4_deployment/unified_runner.py
#
# Usage:
# python phase_4_deployment/unified_runner.py --mode live     # Live trading
# python phase_4_deployment/unified_runner.py --mode paper    # Paper trading
# python phase_4_deployment/unified_runner.py --mode backtest # Backtesting
# python phase_4_deployment/unified_runner.py --mode simulation # Simulation
#
# The unified runner automatically:
# - Starts appropriate trading mode
# - Initializes monitoring and health checks
# - Starts Streamlit dashboard
# - Configures Telegram alerts
# - Handles graceful shutdown
# ENHANCED_TRADING_SYSTEM.md - Comprehensive enhanced system documentation
# ENHANCED_SYSTEM_SUMMARY.md - Implementation summary and validation results
# README.md (updated) - Main system overview with enhanced features
# integration_plan.md (updated) - Phase 3 completion status

# System Cleanup - 2025-05-25
# Files that could interfere with current working system

logging_config.json # Potential conflict with working system
config/whale_config.yaml # Potential conflict with working system
config/schemas/config_schema.json # Potential conflict with working system
phase_4_deployment/carbon_core_config.yaml # Potential conflict with working system
phase_4_deployment/production_config.yaml # Potential conflict with working system
phase_4_deployment/configs/carbon_core_config.yaml # Potential conflict with working system
phase_4_deployment/configs/helius_config.yaml # Potential conflict with working system
phase_4_deployment/configs/jito_config.yaml # Potential conflict with working system
.env.example # Potential conflict with working system
.env.paper # Potential conflict with working system
.env.production # Potential conflict with working system
.env.simulation # Potential conflict with working system
logging_config.json # Potential conflict with working system
pytest.ini # Potential conflict with working system
requirements-test.txt # Potential conflict with working system
requirements.txt.backup # Potential conflict with working system
setup_fallback.py # Potential conflict with working system
simple_paper_trading_monitor.py # Potential conflict with working system
simulation_results.json # Potential conflict with working system
validation_report.json # Potential conflict with working system
test_complete_integration.py # Potential conflict with working system
test_phase1_integration.py # Potential conflict with working system
test_phase2_simple.py # Potential conflict with working system
test_phase3_attribution.py # Potential conflict with working system
backups/ # Potential conflict with working system
configs/ # Potential conflict with working system
utils/ # Potential conflict with working system
tests/unit/ # Potential conflict with working system
tests/core/ # Potential conflict with working system
tests/integration/ # Potential conflict with working system
tests/performance/ # Potential conflict with working system
tests/e2e/ # Potential conflict with working system
tests/functional/ # Potential conflict with working system
tests/shared/ # Potential conflict with working system
tests/utils/ # Potential conflict with working system
BUSINESS_RISK_ASSESSMENT.md # Potential conflict with working system
COMPLETE_INTEGRATION_SUMMARY.md # Potential conflict with working system
DEPLOYMENT_COMPLETE.md # Potential conflict with working system
EMERGENCY_PROCEDURES.md # Potential conflict with working system
ENHANCED_SYSTEM_SUMMARY.md # Potential conflict with working system
ENHANCED_TRADING_SYSTEM.md # Potential conflict with working system
ENTRY_POINTS.md # Potential conflict with working system
FINAL_TEST_SYSTEM_DEPLOYMENT_ALIGNMENT.md # Potential conflict with working system
LIVE_PRODUCTION_STRATEGY_PLAN.md # Potential conflict with working system
LIVE_PRODUCTION_SUCCESS_REPORT.md # Potential conflict with working system
LIVE_PRODUCTION_TEST_RESULTS.md # Potential conflict with working system
LIVE_TRADING_ENTRY_POINTS_ALIGNED.md # Potential conflict with working system
NEXT_ACTIONS_COMPLETED.md # Potential conflict with working system
PHASE1_IMPLEMENTATION_SUMMARY.md # Potential conflict with working system
PHASE2_IMPLEMENTATION_SUMMARY.md # Potential conflict with working system
PHASE4_IMPLEMENTATION_SUMMARY.md # Potential conflict with working system
POSITION_FLATTENING_SOLUTION.md # Potential conflict with working system
PRODUCTION_CONFIGURATION_SUMMARY.md # Potential conflict with working system
RL_LEARNING_SYSTEM.md # Potential conflict with working system
SYSTEM_DOCUMENTATION_UPDATE.md # Potential conflict with working system
SYSTEM_TEST_SUMMARY.md # Potential conflict with working system
Synergy7.md # Potential conflict with working system
TRANSACTION_FIXES_COMPLETE.md # Potential conflict with working system
UPDATED_TEST_SYSTEM_SUMMARY.md # Potential conflict with working system
WALLET_CONFIGURATION_STATUS.md # Potential conflict with working system
WALLET_SETUP_COMPLETE.md # Potential conflict with working system
WHALE_RL_IMPLEMENTATION_PLAN.md # Potential conflict with working system

# ===== REDUNDANT HELIUS TRADING PATHS (2025-05-25 - JITO PRIMARY CONSOLIDATION) =====

# Old test scripts using redundant Helius-only trading paths (replaced by Jito-primary system)
phase_4_deployment/scripts/live_trading.py          # Redundant - uses HeliusExecutor instead of Jito primary
phase_4_deployment/scripts/test_live_trading.py     # Redundant - uses HeliusExecutor instead of Jito primary

# REPLACEMENT INFORMATION:
# These scripts have been superseded by the Jito-primary trading system where:
# - Jito client is primary for all trading operations (MEV protection)
# - Helius is used only for non-trading operations (whale watching, data collection)
# - Built-in fallback from Jito to Helius eliminates redundant fallback logic
# - All trading scripts now use: scripts/unified_live_trading.py or scripts/production_ready_trader.py

# ===== REDUNDANT MODULES AND CONFLICTING IMPLEMENTATIONS (2025-05-25) =====

# ===== LOWER PRIORITY REDUNDANT FILES REMOVED (2025-05-25) =====

# Grafana monitoring components (redundant with Streamlit dashboard)
phase_4_deployment/monitoring/dashboards/                   # REMOVED - Grafana dashboards
phase_4_deployment/monitoring/data/                         # REMOVED - Grafana data directory
phase_4_deployment/monitoring/docker-compose.yml           # REMOVED - Grafana Docker setup
phase_4_deployment/monitoring/grafana.ini                   # REMOVED - Grafana config
phase_4_deployment/monitoring/grafana_dashboard.json        # REMOVED - Grafana dashboard
phase_4_deployment/monitoring/grafana_dashboards.yml        # REMOVED - Grafana dashboard config
phase_4_deployment/monitoring/grafana_datasources.yml       # REMOVED - Grafana datasource config
phase_4_deployment/monitoring/install_requirements.sh       # REMOVED - Grafana installation script
phase_4_deployment/monitoring/streamlit_requirements.txt    # REMOVED - Redundant requirements

# REPLACEMENT INFORMATION:
# Grafana monitoring has been replaced by:
# - phase_4_deployment/unified_dashboard/ (Streamlit dashboard)
# - core/notifications/telegram_notifier.py (Telegram alerts)
# - Existing health check endpoints in trading scripts

# Conflicting CircuitBreaker implementations (causing __init__ parameter errors)
shared/utils/api_helpers.py                         # Uses recovery_timeout instead of reset_timeout
utils/api_helpers.py                                # Uses recovery_timeout instead of reset_timeout
backups/cleanup_backup_20250524_153642/backup/redundant_files/circuit_breaker.py
backups/cleanup_backup_20250524_153642/backups/cleanup_backup_20250524_153642/backup/redundant_files/circuit_breaker.py
backups/cleanup_backup_20250524_153642/backups/cleanup_backup_20250524_153642/backups/cleanup_backup_20250524_153642/backup/redundant_files/circuit_breaker.py

# Redundant backup directories (massive duplication)
backups/cleanup_backup_20250524_153642/             # Entire backup directory tree
backups/                                            # All backup directories

# Conflicting test implementations
tests/test_helius_integration.py                    # Conflicts with new Jito-primary system
tests/test_jito_integration.py                      # May conflict with consolidated implementation
phase_4_deployment/tests/test_solders_fix.py        # Outdated solders testing
phase_4_deployment/tests/test_liljito_client.py     # LilJito deprecated in favor of main Jito client

# Redundant dashboard implementations
phase_4_deployment/unified_dashboard/               # Multiple dashboard implementations
enhanced_trading_dashboard.py                       # Redundant with unified dashboard
simple_monitoring_dashboard.py                      # Redundant with unified dashboard

# Conflicting monitoring services
utils/monitoring/monitoring_service.py              # Conflicts with core monitoring
shared/utils/monitoring.py                          # Conflicts with core monitoring
phase_4_deployment/monitoring/monitoring_service.py # Conflicts with core monitoring

# Redundant configuration files
config/schemas/config_schema.json                   # Potential conflicts
phase_4_deployment/configs/                         # Redundant config directory
configs/                                            # Redundant config directory

# Redundant API helper implementations
shared/utils/                                       # Entire shared utils causing conflicts
utils/                                              # Entire utils directory causing conflicts

# REPLACEMENT INFORMATION:
# These redundant modules cause import conflicts and parameter mismatches.
# The system should use:
# - phase_4_deployment/rpc_execution/jito_client.py (primary trading client)
# - phase_4_deployment/rpc_execution/helius_client.py (non-trading operations)
# - core/ modules for all core functionality
# - phase_4_deployment/ for all deployment-related components

# ===== JUPITER INTEGRATION DEPRECATED (2025-05-26 - REPLACED WITH ORCA) =====

# REASON FOR DEPRECATION: Jupiter integration causing persistent signature verification failures
# REPLACEMENT: Direct Orca Whirlpool program integration for simplified, reliable trading

# Jupiter Transaction Builders (DEPRECATED)
phase_4_deployment/rpc_execution/tx_builder.py                    # Jupiter swap functions: _build_real_jupiter_swap, _get_jupiter_quote, _get_jupiter_swap_transaction
phase_4_deployment/rpc_execution/immediate_jupiter_builder.py     # Complete file - Jupiter immediate execution builder
phase_4_deployment/rpc_execution/enhanced_jupiter_builder.py      # Complete file - Enhanced Jupiter transaction builder

# Jupiter Test Scripts (DEPRECATED)
scripts/test_jupiter_swap.py                                      # Complete file - Jupiter swap testing
scripts/direct_jupiter_test.py                                    # Complete file - Direct Jupiter API testing
scripts/fix_transaction_signing.py                                # Jupiter-specific transaction signing fixes

# Jupiter Configuration Files (DEPRECATED)
config/jupiter_config.yaml                                        # Complete file - Jupiter API configuration
config/jupiter_timing_fix.yaml                                    # Complete file - Jupiter timing optimization
config/transaction_optimization.yaml                              # Jupiter-specific optimization settings
config/optimized_trading.yaml                                     # Jupiter optimization parameters

# Jupiter Utility Functions (DEPRECATED)
solana_tx_utils/solana_tx_utils/fallback.py                      # build_jupiter_swap function only
phase_4_deployment/transaction_execution/__init__.py              # Jupiter-related imports

# Jupiter Dependencies (TO BE REMOVED)
# No specific Jupiter packages in requirements.txt, but HTTP client optimizations for Jupiter API

# REPLACEMENT INFORMATION:
# Jupiter integration has been replaced by:
# - core/dex/orca_client.py - Direct Orca Whirlpool program integration
# - core/dex/orca_swap_builder.py - Simple Orca swap transaction builder
# - core/dex/orca_pool_manager.py - Orca pool discovery and management
# - config/orca_config.yaml - Orca-specific configuration
#
# Benefits of Orca replacement:
# 1. ✅ Direct program calls eliminate signature verification issues
# 2. ✅ Simpler transaction structure reduces complexity
# 3. ✅ Concentrated liquidity AMM provides better pricing
# 4. ✅ No external API dependencies reduce failure points
# 5. ✅ Native Solana program integration improves reliability
#
# Migration completed: 2025-05-26
# All Jupiter references removed and replaced with Orca integration

# ===== DEPRECATED SHELL SCRIPTS (2025-05-26) =====

# Redundant deployment scripts (replaced by unified deployment)
deploy_local_development.sh                                       # Redundant - use phase_4_deployment/unified_runner.py
deploy_production_server.sh                                       # Redundant - use phase_4_deployment/deploy_production.sh
consolidate_project.sh                                            # Redundant - project already consolidated
remove_duplicates.sh                                              # Redundant - duplicates already removed
setup_config_links.sh                                             # Redundant - configs already linked
setup_env.sh                                                      # Redundant - use .env file directly

# Redundant build scripts (replaced by individual component builds)
build_carbon_core.sh                                              # Still used - NOT DEPRECATED
install_requirements.sh                                           # Redundant - use pip install -r requirements.txt
change_prompt.sh                                                  # Redundant utility script

# Redundant monitoring scripts (replaced by unified dashboard)
show_trade_metrics.sh                                             # Redundant - use scripts/display_trade_metrics.py
phase_4_deployment/show_metrics.py                                # Redundant - integrated into dashboard
phase_4_deployment/report_pnl.sh                                  # Redundant - use scripts/analyze_live_metrics_profitability.py
phase_4_deployment/setup_pnl_cron.sh                              # Redundant - use Telegram scheduled reports

# Redundant test scripts (replaced by comprehensive test system)
phase_4_deployment/run_tests.py                                   # Redundant - use tests/run_comprehensive_tests.py
phase_4_deployment/test_dashboard.sh                              # Redundant - dashboard testing integrated
phase_4_deployment/test_end_to_end_simulation.sh                  # Redundant - use comprehensive tests
phase_4_deployment/test_helius_integration.sh                     # Redundant - Helius testing integrated
phase_4_deployment/test_advanced_models.sh                        # Redundant - advanced models testing integrated
phase_4_deployment/verify_simulation.sh                           # Redundant - use scripts/verify_simulation.py

# Redundant dashboard scripts (replaced by unified dashboard)
phase_4_deployment/run_live_dashboard.sh                          # Redundant - use streamlit run directly
phase_4_deployment/run_unified_dashboard.sh                       # Redundant - use streamlit run directly
phase_4_deployment/dashboard/start_dashboard.sh                   # Redundant - use streamlit run directly

# Redundant setup scripts (replaced by automated setup)
phase_4_deployment/setup_dev_environment.sh                       # Redundant - use venv and pip install
phase_4_deployment/generate_jito_keypair.sh                       # Redundant - use Python script
phase_4_deployment/run_load_test.sh                               # Redundant - load testing integrated
phase_4_deployment/run_production_tests.sh                        # Redundant - use comprehensive test system
phase_4_deployment/run_simulation_test.sh                         # Redundant - use Python simulation scripts

# REPLACEMENT INFORMATION:
# These shell scripts have been replaced by:
# - Python-based unified runner (phase_4_deployment/unified_runner.py)
# - Comprehensive test system (tests/run_comprehensive_tests.py)
# - Streamlit dashboard (direct streamlit run commands)
# - Individual Python scripts for specific functions
# - Automated setup through pip and Python package management

# ===== DEPRECATED MODULES (2025-05-26) =====

# Redundant transaction modules (replaced by Orca integration)
core/transaction/enhanced_tx_builder.py                           # Redundant - Jupiter-specific transaction building
core/transaction/enhanced_tx_executor.py                          # Redundant - Jupiter-specific execution
core/wallet/secure_wallet_manager.py                              # Redundant - direct keypair loading used

# Redundant execution modules (replaced by Jito Bundle execution)
core/execution/transaction_executor.py                            # Redundant - replaced by Jito Bundle client
phase_4_deployment/transaction_execution/transaction_executor.py  # Redundant - replaced by unified execution

# Redundant API modules (causing import conflicts)
shared/utils/api_helpers.py                                       # Redundant - conflicts with core API helpers
utils/api_helpers.py                                              # Redundant - conflicts with core API helpers
phase_4_deployment/utils/api_helpers.py.bak                       # Backup file - redundant

# Redundant monitoring modules (replaced by unified monitoring)
core/monitoring/performance_monitor.py                            # Redundant - replaced by system_metrics.py
utils/monitoring/monitoring_service.py                            # Redundant - conflicts with core monitoring
shared/utils/monitoring.py                                        # Redundant - conflicts with core monitoring
phase_4_deployment/monitoring/monitoring_service.py               # Redundant - replaced by unified monitoring

# Redundant strategy modules (replaced by enhanced strategies)
phase_1_strategy_runner/strategies/momentum_strategy.py           # Redundant - replaced by core/strategies/momentum.py
phase_1_strategy_runner/strategies/mean_reversion_strategy.py     # Redundant - replaced by enhanced mean reversion
phase_2_strategy/momentum_strategy.py                             # Redundant - replaced by core strategies
phase_2_strategy/mean_reversion.py                                # Redundant - replaced by core strategies

# Redundant data modules (replaced by centralized data handling)
core/data/                                                        # Redundant directory - data handling centralized
phase_1_strategy_runner/signal_generator.py                       # Redundant - replaced by whale signal generator
phase_1_strategy_runner/signal_processor.py                       # Redundant - replaced by signal enricher

# Redundant engine modules (replaced by unified runner)
core/engine/strategy_runner.py                                    # Redundant - replaced by unified_runner.py
core/engine/unified_runner.py                                     # Redundant - replaced by phase_4_deployment version

# Redundant risk modules (replaced by enhanced risk management)
phase_2_strategy/risk_management.py                               # Redundant - replaced by core/risk/ modules

# ===== DEPRECATED DOCUMENTATION FILES (2025-05-26) =====

# Redundant implementation summaries (replaced by current documentation)
PHASE1_IMPLEMENTATION_SUMMARY.md                                  # Redundant - implementation completed
PHASE2_IMPLEMENTATION_SUMMARY.md                                  # Redundant - implementation completed
PHASE4_IMPLEMENTATION_SUMMARY.md                                  # Redundant - implementation completed
COMPLETE_INTEGRATION_SUMMARY.md                                   # Redundant - integration completed

# Redundant system documentation (replaced by current docs)
ENHANCED_TRADING_SYSTEM.md                                        # Redundant - system is now standard
ENHANCED_SYSTEM_SUMMARY.md                                        # Redundant - enhancements are now standard
SYSTEM_TEST_SUMMARY.md                                            # Redundant - tests updated
UPDATED_TEST_SYSTEM_SUMMARY.md                                    # Redundant - tests updated

# Redundant deployment documentation (replaced by current deployment guide)
DEPLOYMENT_COMPLETE.md                                            # Redundant - deployment is ongoing
PRODUCTION_CONFIGURATION_SUMMARY.md                               # Redundant - configuration documented in main files
LIVE_PRODUCTION_STRATEGY_PLAN.md                                  # Redundant - strategy implemented
LIVE_PRODUCTION_SUCCESS_REPORT.md                                 # Redundant - success is ongoing
LIVE_PRODUCTION_TEST_RESULTS.md                                   # Redundant - testing is ongoing

# Redundant technical documentation (replaced by current implementation)
TRANSACTION_FIXES_COMPLETE.md                                     # Redundant - fixes implemented
JITO_BUNDLE_IMPLEMENTATION_PLAN.md                                # Redundant - implementation completed
JITO_PRIMARY_CONSOLIDATION_SUMMARY.md                             # Redundant - consolidation completed
JITO_SIGNATURE_VERIFICATION_FIX.md                                # Redundant - fix implemented
JITO_SIGNATURE_VERIFICATION_SOLUTION_SUMMARY.md                   # Redundant - solution implemented

# Redundant configuration documentation (replaced by current config files)
WALLET_CONFIGURATION_STATUS.md                                    # Redundant - wallet configured
WALLET_SETUP_COMPLETE.md                                          # Redundant - wallet setup completed
FINAL_ENCODING_SOLUTION_PLAN.md                                   # Redundant - encoding issues resolved
BASE64_ENCODING_FIX_SUMMARY.md                                    # Redundant - encoding fixes implemented
SERIALIZATION_FIX_PLAN.md                                         # Redundant - serialization fixed

# Redundant planning documentation (replaced by current system)
WHALE_RL_IMPLEMENTATION_PLAN.md                                   # Redundant - whale watching implemented
RL_LEARNING_SYSTEM.md                                             # Redundant - RL system integrated
FINAL_RPC_SOLUTION_PLAN.md                                        # Redundant - RPC solution implemented
SYSTEM_FUTURE_PROOFING_SUMMARY.md                                 # Redundant - future-proofing completed

# Redundant status documentation (replaced by current status)
LIVE_TRADING_TEST_SUMMARY.md                                      # Redundant - live trading is operational
MAINNET_SYSTEM_TEST_RESULTS.md                                    # Redundant - mainnet testing completed
REDUNDANT_FILES_REMOVAL_SUMMARY.md                                # Redundant - redundant files managed
NEXT_ACTIONS_COMPLETED.md                                         # Redundant - actions completed

# REPLACEMENT INFORMATION:
# These documentation files have been replaced by:
# - README.md (main system overview)
# - phase_4_deployment/PRODUCTION_DEPLOYMENT.md (deployment guide)
# - tests/README.md (testing documentation)
# - Individual component documentation in respective directories
# - Configuration files with inline comments
# - This depr.txt file for deprecation tracking
