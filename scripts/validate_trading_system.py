#!/usr/bin/env python3
"""
Comprehensive Trading System Validation
Validates all components before live trading execution.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TradingSystemValidator:
    """Comprehensive trading system validator."""
    
    def __init__(self):
        self.validation_results = {}
        
    async def validate_configuration(self):
        """Validate system configuration."""
        logger.info("🔧 Validating system configuration...")
        
        try:
            # Check environment variables
            required_env_vars = [
                'WALLET_ADDRESS',
                'WALLET_PRIVATE_KEY',
                'HELIUS_API_KEY',
                'TELEGRAM_BOT_TOKEN',
                'TELEGRAM_CHAT_ID'
            ]
            
            missing_vars = []
            for var in required_env_vars:
                if not os.environ.get(var):
                    missing_vars.append(var)
            
            if missing_vars:
                logger.error(f"❌ Missing environment variables: {missing_vars}")
                return False
            
            # Check config.yaml
            import yaml
            try:
                with open('config.yaml', 'r') as f:
                    config = yaml.safe_load(f)
                logger.info("✅ config.yaml loaded successfully")
            except Exception as e:
                logger.error(f"❌ Error loading config.yaml: {e}")
                return False
            
            logger.info("✅ Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Configuration validation failed: {e}")
            return False
    
    async def validate_wallet_connection(self):
        """Validate wallet connection and balance."""
        logger.info("💰 Validating wallet connection...")
        
        try:
            from scripts.unified_live_trading import UnifiedLiveTrader
            
            trader = UnifiedLiveTrader()
            
            # Check wallet balance
            balance = await trader.get_wallet_balance()
            logger.info(f"💰 Wallet balance: {balance} SOL")
            
            if balance > 0:
                logger.info("✅ Wallet connection validation passed")
                return True
            else:
                logger.warning("⚠️ Wallet has zero balance")
                return True  # Still valid connection
                
        except Exception as e:
            logger.error(f"❌ Wallet connection validation failed: {e}")
            return False
    
    async def validate_rpc_clients(self):
        """Validate RPC client connections."""
        logger.info("🌐 Validating RPC client connections...")
        
        try:
            # Test Helius connection
            import httpx
            helius_url = f"https://mainnet.helius-rpc.com/?api-key={os.environ.get('HELIUS_API_KEY')}"
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                payload = {
                    'jsonrpc': '2.0',
                    'id': 1,
                    'method': 'getHealth'
                }
                response = await client.post(helius_url, json=payload)
                result = response.json()
                
                if response.status_code == 200:
                    logger.info("✅ Helius RPC connection validated")
                else:
                    logger.error(f"❌ Helius RPC connection failed: {result}")
                    return False
            
            logger.info("✅ RPC client validation passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ RPC client validation failed: {e}")
            return False
    
    async def validate_telegram_notifications(self):
        """Validate Telegram notification system."""
        logger.info("📱 Validating Telegram notifications...")
        
        try:
            from core.notifications.telegram_notifier import TelegramNotifier
            
            notifier = TelegramNotifier()
            
            # Send test notification
            test_message = f"🧪 System validation test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            success = await notifier.send_message(test_message)
            
            if success:
                logger.info("✅ Telegram notification validation passed")
                return True
            else:
                logger.error("❌ Telegram notification validation failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Telegram notification validation failed: {e}")
            return False
    
    async def validate_transaction_components(self):
        """Validate transaction building and execution components."""
        logger.info("🔧 Validating transaction components...")
        
        try:
            from scripts.unified_live_trading import UnifiedLiveTrader
            
            trader = UnifiedLiveTrader()
            
            # Initialize components
            if await trader.initialize_components():
                logger.info("✅ Transaction components initialized successfully")
                
                # Test transaction building (dry run)
                test_signal = {
                    'action': 'BUY',
                    'market': 'SOL-USDC',
                    'price': 180.0,
                    'size': 0.001,
                    'confidence': 0.8,
                    'timestamp': datetime.now().isoformat()
                }
                
                # Test with dry run
                trader.dry_run = True
                result = await trader.execute_trade(test_signal)
                
                if result and result.get('success'):
                    logger.info("✅ Transaction component validation passed")
                    return True
                else:
                    logger.error("❌ Transaction component validation failed")
                    return False
            else:
                logger.error("❌ Failed to initialize transaction components")
                return False
                
        except Exception as e:
            logger.error(f"❌ Transaction component validation failed: {e}")
            return False
    
    async def run_full_validation(self):
        """Run complete system validation."""
        logger.info("🚀 STARTING COMPREHENSIVE SYSTEM VALIDATION")
        logger.info("="*60)
        
        validations = [
            ("Configuration", self.validate_configuration),
            ("Wallet Connection", self.validate_wallet_connection),
            ("RPC Clients", self.validate_rpc_clients),
            ("Telegram Notifications", self.validate_telegram_notifications),
            ("Transaction Components", self.validate_transaction_components)
        ]
        
        passed = 0
        total = len(validations)
        
        for name, validation_func in validations:
            logger.info(f"\n📋 Validating {name}...")
            try:
                result = await validation_func()
                self.validation_results[name] = result
                if result:
                    passed += 1
                    logger.info(f"✅ {name}: PASSED")
                else:
                    logger.error(f"❌ {name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {name}: ERROR - {e}")
                self.validation_results[name] = False
        
        # Summary
        logger.info("\n" + "="*60)
        logger.info("VALIDATION SUMMARY")
        logger.info("="*60)
        
        for name, result in self.validation_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{name}: {status}")
        
        success_rate = (passed / total) * 100
        logger.info(f"\nOverall Success Rate: {success_rate:.1f}% ({passed}/{total})")
        
        if passed == total:
            logger.info("🎉 ALL VALIDATIONS PASSED - SYSTEM READY FOR LIVE TRADING")
            return True
        else:
            logger.error("⚠️ SOME VALIDATIONS FAILED - SYSTEM NEEDS FIXES")
            return False

async def main():
    """Main validation function."""
    validator = TradingSystemValidator()
    success = await validator.run_full_validation()
    
    if success:
        print("\n✅ SYSTEM VALIDATION SUCCESSFUL")
        print("🚀 Ready to proceed with live trading")
        return 0
    else:
        print("\n❌ SYSTEM VALIDATION FAILED")
        print("🔧 Please fix the issues before proceeding")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
