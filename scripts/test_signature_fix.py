#!/usr/bin/env python3
"""
Test Signature Verification Fix
This script tests the signature verification fix with a simple transaction.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_signature_verification_fix():
    """Test the signature verification fix."""
    logger.info("🧪 TESTING SIGNATURE VERIFICATION FIX")
    logger.info("="*60)
    
    try:
        # Import the unified trading system
        from scripts.unified_live_trading import UnifiedLiveTrader
        
        # Create trader with test configuration
        trader = UnifiedLiveTrader()
        trader.dry_run = False  # Test with actual execution but very small amounts
        trader.trading_enabled = True
        
        # Initialize components
        logger.info("🔧 Initializing trading components...")
        if not await trader.initialize_components():
            logger.error("❌ Failed to initialize components")
            return False
        
        # Check wallet balance
        logger.info("💰 Checking wallet balance...")
        balance = await trader.get_wallet_balance()
        logger.info(f"💰 Current wallet balance: {balance} SOL")
        
        if balance < 0.01:
            logger.error("❌ Insufficient wallet balance for testing")
            return False
        
        # Create a minimal test signal
        test_signal = {
            'action': 'BUY',
            'market': 'SOL-USDC',
            'price': 180.0,
            'size': 0.0001,  # Very small amount for testing
            'confidence': 0.9,
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"🎯 Testing with signal: {test_signal}")
        
        # Execute the test trade
        logger.info("💸 Executing test trade...")
        result = await trader.execute_trade(test_signal)
        
        if result and result.get('success'):
            logger.info("✅ SIGNATURE VERIFICATION FIX SUCCESSFUL!")
            logger.info(f"✅ Trade executed successfully: {result.get('signature', 'N/A')}")
            return True
        else:
            error = result.get('error', 'Unknown error') if result else 'No result returned'
            logger.error(f"❌ SIGNATURE VERIFICATION FIX FAILED: {error}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in signature verification test: {e}")
        return False

async def main():
    """Main function."""
    print("\n" + "="*60)
    print("SIGNATURE VERIFICATION FIX TEST")
    print("="*60)
    print("This test will:")
    print("1. Initialize the fixed trading system")
    print("2. Execute a very small test trade")
    print("3. Verify signature verification works")
    print("4. Confirm successful trade execution")
    print("="*60)
    
    # Confirm with user
    response = input("\nProceed with signature verification test? (yes/no): ").lower().strip()
    if response not in ['yes', 'y']:
        print("❌ Test cancelled by user")
        return 1
    
    # Run the test
    success = await test_signature_verification_fix()
    
    if success:
        print("\n✅ SIGNATURE VERIFICATION FIX SUCCESSFUL")
        print("🎯 Trading system is now working correctly")
        print("🚀 Ready for live trading")
        return 0
    else:
        print("\n❌ SIGNATURE VERIFICATION FIX FAILED")
        print("🔧 Additional debugging required")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
