#!/usr/bin/env python3
"""
Testnet Environment Setup Script
Sets up complete testnet environment for end-to-end testing including real transaction execution.
"""

import os
import sys
import json
import asyncio
import logging
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestnetEnvironmentSetup:
    """Setup and configure testnet environment for full system testing."""
    
    def __init__(self):
        """Initialize testnet setup."""
        self.project_root = Path(__file__).parent.parent
        self.testnet_config_path = self.project_root / "config" / "environments" / "testnet.yaml"
        self.testnet_env_path = self.project_root / ".env.testnet"
        self.testnet_keypair_path = self.project_root / "wallet" / "testnet_keypair.json"
        
    async def setup_complete_testnet_environment(self) -> bool:
        """Set up complete testnet environment."""
        logger.info("🚀 Setting up Synergy7 Testnet Environment")
        
        try:
            # Step 1: Create testnet keypair
            if not await self.create_testnet_keypair():
                return False
            
            # Step 2: Request testnet SOL
            if not await self.request_testnet_sol():
                return False
            
            # Step 3: Create testnet environment file
            if not await self.create_testnet_env_file():
                return False
            
            # Step 4: Validate testnet configuration
            if not await self.validate_testnet_config():
                return False
            
            # Step 5: Test basic connectivity
            if not await self.test_testnet_connectivity():
                return False
            
            logger.info("✅ Testnet environment setup completed successfully!")
            logger.info("🎯 Ready for full end-to-end testing with real transaction execution")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error setting up testnet environment: {e}")
            return False
    
    async def create_testnet_keypair(self) -> bool:
        """Create a new testnet keypair."""
        logger.info("🔑 Creating testnet keypair...")
        
        try:
            # Ensure wallet directory exists
            wallet_dir = self.project_root / "wallet"
            wallet_dir.mkdir(exist_ok=True)
            
            # Generate new keypair using Solana CLI if available
            try:
                result = subprocess.run([
                    "solana-keygen", "new", 
                    "--outfile", str(self.testnet_keypair_path),
                    "--no-bip39-passphrase",
                    "--force"
                ], capture_output=True, text=True, check=True)
                
                logger.info("✅ Testnet keypair created using Solana CLI")
                
                # Get the public key
                pubkey_result = subprocess.run([
                    "solana-keygen", "pubkey", str(self.testnet_keypair_path)
                ], capture_output=True, text=True, check=True)
                
                self.testnet_wallet_address = pubkey_result.stdout.strip()
                logger.info(f"📍 Testnet wallet address: {self.testnet_wallet_address}")
                
                return True
                
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.warning("⚠️ Solana CLI not found, using Python keypair generation")
                return await self.create_keypair_python()
                
        except Exception as e:
            logger.error(f"❌ Error creating testnet keypair: {e}")
            return False
    
    async def create_keypair_python(self) -> bool:
        """Create keypair using Python (fallback method)."""
        try:
            from solders.keypair import Keypair
            
            # Generate new keypair
            keypair = Keypair()
            self.testnet_wallet_address = str(keypair.pubkey())
            
            # Save keypair to file
            keypair_bytes = bytes(keypair)
            keypair_array = list(keypair_bytes)
            
            with open(self.testnet_keypair_path, 'w') as f:
                json.dump(keypair_array, f)
            
            # Set proper permissions
            os.chmod(self.testnet_keypair_path, 0o600)
            
            logger.info("✅ Testnet keypair created using Python")
            logger.info(f"📍 Testnet wallet address: {self.testnet_wallet_address}")
            
            return True
            
        except ImportError:
            logger.error("❌ solders library not available for keypair generation")
            return False
        except Exception as e:
            logger.error(f"❌ Error creating Python keypair: {e}")
            return False
    
    async def request_testnet_sol(self) -> bool:
        """Request SOL from testnet faucet."""
        logger.info("💰 Requesting SOL from testnet faucet...")
        
        try:
            # Try using Solana CLI faucet
            try:
                result = subprocess.run([
                    "solana", "airdrop", "2", self.testnet_wallet_address,
                    "--url", "testnet"
                ], capture_output=True, text=True, check=True)
                
                logger.info("✅ Successfully requested 2 SOL from testnet faucet")
                logger.info(f"📊 Faucet response: {result.stdout.strip()}")
                
                return True
                
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.warning("⚠️ Solana CLI not found, using HTTP faucet request")
                return await self.request_sol_http()
                
        except Exception as e:
            logger.error(f"❌ Error requesting testnet SOL: {e}")
            return False
    
    async def request_sol_http(self) -> bool:
        """Request SOL using HTTP faucet."""
        try:
            import httpx
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://faucet.solana.com/",
                    json={
                        "pubkey": self.testnet_wallet_address,
                        "amount": 2000000000  # 2 SOL in lamports
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    logger.info("✅ Successfully requested SOL via HTTP faucet")
                    return True
                else:
                    logger.warning(f"⚠️ Faucet request failed: {response.status_code}")
                    logger.info("💡 You may need to manually request SOL from https://faucet.solana.com/")
                    return True  # Continue anyway
                    
        except Exception as e:
            logger.warning(f"⚠️ HTTP faucet request failed: {e}")
            logger.info("💡 You may need to manually request SOL from https://faucet.solana.com/")
            return True  # Continue anyway
    
    async def create_testnet_env_file(self) -> bool:
        """Create testnet environment file."""
        logger.info("📝 Creating testnet environment file...")
        
        try:
            env_content = f"""# Synergy7 Testnet Environment Configuration
# This file configures the system for testnet operation with real transaction execution

# Network Configuration
SOLANA_NETWORK=testnet
HELIUS_RPC_URL=https://api.testnet.solana.com
FALLBACK_RPC_URL=https://api.testnet.solana.com

# Wallet Configuration (Testnet)
TESTNET_WALLET_ADDRESS={self.testnet_wallet_address}
WALLET_ADDRESS={self.testnet_wallet_address}
KEYPAIR_PATH=wallet/testnet_keypair.json

# API Keys (use your existing keys)
HELIUS_API_KEY={os.getenv('HELIUS_API_KEY', 'your_helius_api_key')}
BIRDEYE_API_KEY={os.getenv('BIRDEYE_API_KEY', 'your_birdeye_api_key')}

# Trading Configuration (Testnet)
TRADING_ENABLED=true
DRY_RUN=false
PAPER_TRADING=false
TESTNET_MODE=true

# Risk Management (Conservative for testing)
MAX_POSITION_SIZE_PCT=0.05
MAX_POSITION_SIZE_USD=10
DAILY_LOSS_LIMIT_USD=5

# Execution Configuration
USE_JITO=false
SLIPPAGE_TOLERANCE=0.05
MAX_RETRIES=5
TIMEOUT=60

# Telegram Notifications
TELEGRAM_BOT_TOKEN={os.getenv('TELEGRAM_BOT_TOKEN', '')}
TELEGRAM_CHAT_ID={os.getenv('TELEGRAM_CHAT_ID', '')}

# Logging
LOG_LEVEL=DEBUG
RUST_BACKTRACE=1
PYTHONUNBUFFERED=1

# System Settings
ENVIRONMENT=testnet
CONFIG_PATH=config/environments/testnet.yaml
"""
            
            with open(self.testnet_env_path, 'w') as f:
                f.write(env_content)
            
            logger.info(f"✅ Created testnet environment file: {self.testnet_env_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error creating testnet env file: {e}")
            return False
    
    async def validate_testnet_config(self) -> bool:
        """Validate testnet configuration."""
        logger.info("🔍 Validating testnet configuration...")
        
        try:
            # Check if testnet config file exists
            if not self.testnet_config_path.exists():
                logger.error(f"❌ Testnet config file not found: {self.testnet_config_path}")
                return False
            
            # Check if keypair file exists and has correct permissions
            if not self.testnet_keypair_path.exists():
                logger.error(f"❌ Testnet keypair file not found: {self.testnet_keypair_path}")
                return False
            
            # Check keypair permissions
            keypair_stat = os.stat(self.testnet_keypair_path)
            if keypair_stat.st_mode & 0o077:
                logger.warning("⚠️ Keypair file has overly permissive permissions, fixing...")
                os.chmod(self.testnet_keypair_path, 0o600)
            
            logger.info("✅ Testnet configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error validating testnet config: {e}")
            return False
    
    async def test_testnet_connectivity(self) -> bool:
        """Test basic testnet connectivity."""
        logger.info("🌐 Testing testnet connectivity...")
        
        try:
            import httpx
            
            # Test RPC connectivity
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.testnet.solana.com",
                    json={
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "getHealth"
                    },
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    logger.info("✅ Testnet RPC connectivity successful")
                else:
                    logger.warning(f"⚠️ Testnet RPC returned status: {response.status_code}")
            
            # Test wallet balance
            balance_response = await client.post(
                "https://api.testnet.solana.com",
                json={
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getBalance",
                    "params": [self.testnet_wallet_address]
                },
                timeout=10.0
            )
            
            if balance_response.status_code == 200:
                balance_data = balance_response.json()
                if 'result' in balance_data:
                    balance_lamports = balance_data['result']['value']
                    balance_sol = balance_lamports / 1_000_000_000
                    logger.info(f"💰 Testnet wallet balance: {balance_sol:.6f} SOL")
                    
                    if balance_sol < 0.1:
                        logger.warning("⚠️ Low testnet balance, consider requesting more SOL from faucet")
                else:
                    logger.warning("⚠️ Could not retrieve wallet balance")
            
            logger.info("✅ Testnet connectivity test completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error testing testnet connectivity: {e}")
            return False
    
    def print_testnet_instructions(self):
        """Print instructions for using testnet environment."""
        logger.info("\n" + "="*60)
        logger.info("🎯 TESTNET ENVIRONMENT READY")
        logger.info("="*60)
        logger.info(f"📍 Testnet Wallet: {self.testnet_wallet_address}")
        logger.info(f"🔑 Keypair File: {self.testnet_keypair_path}")
        logger.info(f"⚙️ Environment File: {self.testnet_env_path}")
        logger.info(f"📋 Config File: {self.testnet_config_path}")
        logger.info("\n🚀 TO START TESTNET TRADING:")
        logger.info("   python3 phase_4_deployment/unified_runner.py --mode live --env-file .env.testnet")
        logger.info("\n📊 TO START TESTNET DASHBOARD:")
        logger.info("   streamlit run phase_4_deployment/dashboard/streamlit_dashboard.py --server.port 8502")
        logger.info("\n🔍 TO MONITOR TRANSACTIONS:")
        logger.info(f"   https://explorer.solana.com/address/{self.testnet_wallet_address}?cluster=testnet")
        logger.info("\n💰 TO REQUEST MORE SOL:")
        logger.info("   https://faucet.solana.com/")
        logger.info("="*60)

async def main():
    """Main function."""
    setup = TestnetEnvironmentSetup()
    
    if await setup.setup_complete_testnet_environment():
        setup.print_testnet_instructions()
        return True
    else:
        logger.error("❌ Failed to set up testnet environment")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
