"""
Orca Swap Builder
Builds swap transactions for Orca DEX integration.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from decimal import Decimal
import json
import os

# Solana imports
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solders.transaction import Transaction

# Local imports
from core.dex.orca_client import OrcaClient, get_token_mint

logger = logging.getLogger(__name__)

class OrcaSwapBuilder:
    """Builds swap transactions using Orca DEX."""
    
    def __init__(self, wallet_address: str, keypair_path: str = "wallet/trading_wallet_keypair.json"):
        """
        Initialize Orca swap builder.
        
        Args:
            wallet_address: Wallet address
            keypair_path: Path to keypair file
        """
        self.wallet_address = wallet_address
        self.keypair_path = keypair_path
        self.keypair = None
        self.orca_client = None
        
        logger.info("🔨 Orca swap builder initialized")
    
    async def initialize(self):
        """Initialize the swap builder."""
        try:
            # Load keypair
            await self._load_keypair()
            
            # Initialize Orca client
            self.orca_client = OrcaClient()
            
            logger.info("✅ Orca swap builder ready")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Orca swap builder: {e}")
            return False
    
    async def _load_keypair(self):
        """Load keypair from file."""
        try:
            with open(self.keypair_path, 'r') as f:
                keypair_data = json.load(f)
            
            self.keypair = Keypair.from_bytes(bytes(keypair_data))
            
            # Verify keypair matches wallet address
            if str(self.keypair.pubkey()) != self.wallet_address:
                logger.warning(f"⚠️ Keypair mismatch: {self.keypair.pubkey()} != {self.wallet_address}")
            
            logger.info("✅ Keypair loaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to load keypair: {e}")
            raise
    
    async def build_swap_transaction(self, signal: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Build a swap transaction from a trading signal.
        
        Args:
            signal: Trading signal with action, market, size, etc.
            
        Returns:
            Transaction result or None if failed
        """
        try:
            logger.info(f"🔨 Building Orca swap for signal: {signal}")
            
            # Parse signal
            action = signal.get('action', '').upper()
            market = signal.get('market', '')
            size = signal.get('size', 0)
            price = signal.get('price', 0)
            
            # Parse market pair
            if '-' not in market:
                logger.error(f"❌ Invalid market format: {market}")
                return None
            
            base_token, quote_token = market.split('-', 1)
            
            # Determine input/output tokens based on action
            if action == 'BUY':
                # Buy base token with quote token
                input_token = get_token_mint(quote_token)
                output_token = get_token_mint(base_token)
                # Convert size to quote token amount
                amount = int(size * price * 1_000_000)  # Assuming USDC (6 decimals)
            elif action == 'SELL':
                # Sell base token for quote token
                input_token = get_token_mint(base_token)
                output_token = get_token_mint(quote_token)
                # Convert size to base token amount
                amount = int(size * 1_000_000_000)  # Assuming SOL (9 decimals)
            else:
                logger.error(f"❌ Invalid action: {action}")
                return None
            
            logger.info(f"💱 Swap: {amount} {input_token[:8]}... -> {output_token[:8]}...")
            
            # Get quote from Orca
            quote = await self.orca_client.get_quote(input_token, output_token, amount)
            if not quote:
                logger.error("❌ Failed to get Orca quote")
                return None
            
            # Build transaction
            transaction = await self.orca_client.build_swap_transaction(quote, self.keypair)
            if not transaction:
                logger.error("❌ Failed to build Orca transaction")
                return None
            
            # Serialize transaction
            tx_bytes = bytes(transaction)
            
            # Return transaction data
            result = {
                'success': True,
                'transaction': tx_bytes,
                'quote': quote,
                'execution_type': 'orca_swap',
                'input_token': input_token,
                'output_token': output_token,
                'input_amount': amount,
                'estimated_output': quote.get('estimated_output', 0),
                'min_output': quote.get('min_output', 0),
                'slippage_bps': quote.get('slippage_bps', 50)
            }
            
            logger.info("✅ Orca swap transaction built successfully")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error building Orca swap transaction: {e}")
            return {
                'success': False,
                'error': str(e),
                'execution_type': 'orca_swap'
            }
    
    async def estimate_swap_output(self, input_token: str, output_token: str, amount: int) -> Optional[Dict[str, Any]]:
        """
        Estimate swap output without building transaction.
        
        Args:
            input_token: Input token mint
            output_token: Output token mint
            amount: Input amount
            
        Returns:
            Estimation data or None if failed
        """
        try:
            if not self.orca_client:
                await self.initialize()
            
            quote = await self.orca_client.get_quote(input_token, output_token, amount)
            if quote:
                return {
                    'estimated_output': quote.get('estimated_output', 0),
                    'min_output': quote.get('min_output', 0),
                    'price_impact_pct': quote.get('price_impact_pct', 0),
                    'slippage_bps': quote.get('slippage_bps', 50)
                }
            return None
            
        except Exception as e:
            logger.error(f"❌ Error estimating swap output: {e}")
            return None
    
    async def get_supported_tokens(self) -> Dict[str, str]:
        """Get list of supported tokens."""
        try:
            if not self.orca_client:
                await self.initialize()
            
            # Return configured tokens
            tokens_config = self.orca_client.config.get('tokens', {})
            return {symbol: info['mint'] for symbol, info in tokens_config.items()}
            
        except Exception as e:
            logger.error(f"❌ Error getting supported tokens: {e}")
            return {}
    
    async def get_pool_info(self, token_a: str, token_b: str) -> Optional[Dict[str, Any]]:
        """Get pool information for a token pair."""
        try:
            if not self.orca_client:
                await self.initialize()
            
            return await self.orca_client.get_pool_info(token_a, token_b)
            
        except Exception as e:
            logger.error(f"❌ Error getting pool info: {e}")
            return None
    
    async def validate_swap_parameters(self, signal: Dict[str, Any]) -> bool:
        """
        Validate swap parameters before building transaction.
        
        Args:
            signal: Trading signal
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check required fields
            required_fields = ['action', 'market', 'size', 'price']
            for field in required_fields:
                if field not in signal:
                    logger.error(f"❌ Missing required field: {field}")
                    return False
            
            # Validate action
            action = signal.get('action', '').upper()
            if action not in ['BUY', 'SELL']:
                logger.error(f"❌ Invalid action: {action}")
                return False
            
            # Validate market format
            market = signal.get('market', '')
            if '-' not in market:
                logger.error(f"❌ Invalid market format: {market}")
                return False
            
            # Validate numeric values
            size = signal.get('size', 0)
            price = signal.get('price', 0)
            
            if size <= 0:
                logger.error(f"❌ Invalid size: {size}")
                return False
            
            if price <= 0:
                logger.error(f"❌ Invalid price: {price}")
                return False
            
            logger.info("✅ Swap parameters validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error validating swap parameters: {e}")
            return False
    
    async def close(self):
        """Close the swap builder and cleanup resources."""
        try:
            if self.orca_client:
                await self.orca_client.close()
            logger.info("✅ Orca swap builder closed")
        except Exception as e:
            logger.error(f"❌ Error closing Orca swap builder: {e}")

# Utility functions
def parse_market_pair(market: str) -> tuple:
    """Parse market string into base and quote tokens."""
    if '-' not in market:
        raise ValueError(f"Invalid market format: {market}")
    return market.split('-', 1)

def calculate_token_amount(size: float, decimals: int) -> int:
    """Calculate token amount in smallest units."""
    return int(size * (10 ** decimals))

def format_token_amount(amount: int, decimals: int) -> float:
    """Format token amount from smallest units to human readable."""
    return amount / (10 ** decimals)
