"""
Orca DEX Client
Direct integration with Orca Whirlpool program for reliable trading.
"""

import asyncio
import logging
import os
import yaml
from typing import Dict, Any, Optional, List, Tuple
from decimal import Decimal
import httpx

# Solana imports
from solders.pubkey import Pubkey
from solders.keypair import Keypair
from solders.instruction import Instruction, AccountMeta
from solders.transaction import Transaction
from solders.message import Message
from solders.hash import Hash
from solders.system_program import transfer, TransferParams

logger = logging.getLogger(__name__)

class OrcaClient:
    """Direct Orca Whirlpool program client for reliable trading."""
    
    def __init__(self, rpc_url: str = None, config_path: str = "config/orca_config.yaml"):
        """
        Initialize Orca client.
        
        Args:
            rpc_url: RPC endpoint URL
            config_path: Path to Orca configuration file
        """
        self.rpc_url = rpc_url or f"https://mainnet.helius-rpc.com/?api-key={os.environ.get('HELIUS_API_KEY')}"
        self.config = self._load_config(config_path)
        self.http_client = None
        
        # Program IDs
        self.whirlpool_program_id = Pubkey.from_string(self.config['program']['whirlpool_program_id'])
        self.token_program_id = Pubkey.from_string(self.config['program']['token_program_id'])
        self.system_program_id = Pubkey.from_string(self.config['program']['system_program_id'])
        
        # Pool cache
        self.pool_cache = {}
        self.token_cache = {}
        
        logger.info("✅ Orca client initialized")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load Orca configuration from file."""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"✅ Loaded Orca config from {config_path}")
            return config
        except Exception as e:
            logger.error(f"❌ Failed to load Orca config: {e}")
            # Return default config
            return {
                'program': {
                    'whirlpool_program_id': 'whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc',
                    'token_program_id': 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
                    'system_program_id': '11111111111111111111111111111111'
                },
                'transaction': {
                    'default_slippage_bps': 50,
                    'compute_unit_limit': 200000
                }
            }
    
    async def get_pool_info(self, token_a: str, token_b: str) -> Optional[Dict[str, Any]]:
        """
        Get pool information for a token pair.
        
        Args:
            token_a: First token mint address
            token_b: Second token mint address
            
        Returns:
            Pool information or None if not found
        """
        try:
            # Check cache first
            cache_key = f"{token_a}-{token_b}"
            if cache_key in self.pool_cache:
                logger.debug(f"📋 Using cached pool info for {cache_key}")
                return self.pool_cache[cache_key]
            
            # For now, use static configuration
            # In a full implementation, this would query the blockchain
            pools_config = self.config.get('pools', {})
            
            for pool_name, pool_info in pools_config.items():
                if ((pool_info['token_a'] == token_a and pool_info['token_b'] == token_b) or
                    (pool_info['token_a'] == token_b and pool_info['token_b'] == token_a)):
                    
                    # Cache the result
                    self.pool_cache[cache_key] = pool_info
                    logger.info(f"✅ Found pool {pool_name} for {token_a}-{token_b}")
                    return pool_info
            
            logger.warning(f"⚠️ No pool found for {token_a}-{token_b}")
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting pool info: {e}")
            return None
    
    async def get_quote(self, input_mint: str, output_mint: str, amount: int, slippage_bps: int = None) -> Optional[Dict[str, Any]]:
        """
        Get a quote for a swap.
        
        Args:
            input_mint: Input token mint address
            output_mint: Output token mint address
            amount: Input amount in token's smallest unit
            slippage_bps: Slippage tolerance in basis points
            
        Returns:
            Quote information or None if failed
        """
        try:
            # Get pool information
            pool_info = await self.get_pool_info(input_mint, output_mint)
            if not pool_info:
                logger.error(f"❌ No pool found for {input_mint} -> {output_mint}")
                return None
            
            # For now, use a simple calculation
            # In a full implementation, this would use the actual pool state
            slippage_bps = slippage_bps or self.config['transaction']['default_slippage_bps']
            
            # Simple 1:1 ratio for demonstration (would be replaced with actual pool math)
            # This is a placeholder - real implementation would calculate based on pool reserves
            estimated_output = amount  # Simplified
            
            # Apply slippage
            slippage_factor = (10000 - slippage_bps) / 10000
            min_output = int(estimated_output * slippage_factor)
            
            quote = {
                'input_mint': input_mint,
                'output_mint': output_mint,
                'input_amount': amount,
                'estimated_output': estimated_output,
                'min_output': min_output,
                'slippage_bps': slippage_bps,
                'pool_info': pool_info,
                'price_impact_pct': 0.1  # Placeholder
            }
            
            logger.info(f"💰 Quote: {amount} -> {estimated_output} (min: {min_output})")
            return quote
            
        except Exception as e:
            logger.error(f"❌ Error getting quote: {e}")
            return None
    
    async def build_swap_instruction(self, quote: Dict[str, Any], user_pubkey: Pubkey) -> Optional[Instruction]:
        """
        Build a swap instruction for Orca.
        
        Args:
            quote: Quote information from get_quote
            user_pubkey: User's public key
            
        Returns:
            Swap instruction or None if failed
        """
        try:
            # This is a simplified implementation
            # A full implementation would build the actual Orca swap instruction
            
            # For now, create a simple transfer instruction as a placeholder
            # This demonstrates the transaction structure without complex Orca logic
            
            logger.info("🔨 Building simplified swap instruction (placeholder)")
            
            # Create a minimal transfer instruction (placeholder for actual swap)
            transfer_ix = transfer(
                TransferParams(
                    from_pubkey=user_pubkey,
                    to_pubkey=user_pubkey,  # Self-transfer for testing
                    lamports=1000  # Minimal amount
                )
            )
            
            logger.info("✅ Swap instruction built (simplified)")
            return transfer_ix
            
        except Exception as e:
            logger.error(f"❌ Error building swap instruction: {e}")
            return None
    
    async def build_swap_transaction(self, quote: Dict[str, Any], user_keypair: Keypair) -> Optional[Transaction]:
        """
        Build a complete swap transaction.
        
        Args:
            quote: Quote information
            user_keypair: User's keypair
            
        Returns:
            Signed transaction or None if failed
        """
        try:
            logger.info("🔨 Building Orca swap transaction...")
            
            # Build swap instruction
            swap_ix = await self.build_swap_instruction(quote, user_keypair.pubkey())
            if not swap_ix:
                logger.error("❌ Failed to build swap instruction")
                return None
            
            # Get recent blockhash
            recent_blockhash = await self._get_recent_blockhash()
            if not recent_blockhash:
                logger.error("❌ Failed to get recent blockhash")
                return None
            
            # Create message
            message = Message.new_with_blockhash(
                [swap_ix],
                user_keypair.pubkey(),
                recent_blockhash
            )
            
            # Create and sign transaction
            transaction = Transaction.new_unsigned(message)
            transaction.sign([user_keypair], recent_blockhash)
            
            logger.info("✅ Orca swap transaction built and signed")
            return transaction
            
        except Exception as e:
            logger.error(f"❌ Error building swap transaction: {e}")
            return None
    
    async def _get_recent_blockhash(self) -> Optional[Hash]:
        """Get recent blockhash from RPC."""
        try:
            if not self.http_client:
                self.http_client = httpx.AsyncClient(timeout=10.0)
            
            payload = {
                'jsonrpc': '2.0',
                'id': 1,
                'method': 'getLatestBlockhash',
                'params': [{'commitment': 'confirmed'}]
            }
            
            response = await self.http_client.post(self.rpc_url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if 'result' in result and 'value' in result['result']:
                blockhash_str = result['result']['value']['blockhash']
                return Hash.from_string(blockhash_str)
            else:
                logger.error(f"❌ Invalid blockhash response: {result}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error getting recent blockhash: {e}")
            return None
    
    async def close(self):
        """Close the HTTP client."""
        if self.http_client:
            await self.http_client.aclose()
            logger.info("✅ Orca client closed")

# Token mint addresses for common tokens
COMMON_TOKENS = {
    'SOL': 'So11111111111111111111111111111111111111112',
    'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
}

def get_token_mint(symbol: str) -> str:
    """Get token mint address by symbol."""
    return COMMON_TOKENS.get(symbol.upper(), symbol)
